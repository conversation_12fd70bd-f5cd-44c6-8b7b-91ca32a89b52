<template>
  <div class="login-container">
    <div class="brand-logo">
      <img class="logo" src="@/assets/login-logo.png">
    </div>
    <!-- 动画 -->
    <div class="main-content">
      <transition name="el-fade-in-linear" mode="out-in" leave-active-class="">
        <div class="login-section" v-show="currentStep === 1">
          <div class="login-header">
            <img class="login-welcome" src="@/assets/login-welcome.png" />
            <div class="login-tabs">
              <div class="tab-item" :class="{ active: loginForm.user_type === 1 }" @click="changeUserType(1)">
                EDUCATION PROVIDER LOGIN
              </div>
              <div class="tab-item" :class="{ active: loginForm.user_type === 2 }" @click="changeUserType(2)">
                STUDENT LOGIN
              </div>
            </div>
          </div>

          <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form"
            @submit.prevent="handleLogin">
            <el-form-item prop="username">
              <div class="input-wrapper">
                <el-icon class="input-icon">
                  <User />
                </el-icon>
                <el-input v-model="loginForm.username" placeholder="Username" size="large" clearable
                  @keyup.enter="handleLogin" />
              </div>
            </el-form-item>

            <el-form-item prop="password">
              <div class="input-wrapper">
                <el-icon class="input-icon">
                  <Lock />
                </el-icon>
                <el-input v-model="loginForm.password" type="password" placeholder="Password" size="large" show-password
                  clearable @keyup.enter="handleLogin" />
              </div>
            </el-form-item>

            <div class="forgot-password">
              <el-link type="primary" underline="never">Forgot Password</el-link>
            </div>

            <el-form-item>
              <el-button type="primary" size="large" class="login-button" :loading="authStore.isLoading"
                @click="handleLogin">
                Login
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer">
            <p>No account? <el-link type="primary" underline="never" class="register-link">Register now</el-link></p>

            <div class="divider">
              <span>OR</span>
            </div>

            <!-- 社交登录 -->
            <div class="social-login">
              <div class="social-icon" @click="handleSocialLogin('facebook')">
                <img class="icon" src="@/assets/login-options-1.png" alt="facebook">
              </div>
              <div class="social-icon" @click="handleSocialLogin('pinterest')">
                <img class="icon" src="@/assets/login-options-2.png" alt="pinterest">
              </div>
              <div class="social-icon" @click="handleSocialLogin('github')">
                <img class="icon" src="@/assets/login-options-3.png" alt="github">
              </div>
              <div class="social-icon" @click="handleSocialLogin('twitter')">
                <img class="icon" src="@/assets/login-options-4.png" alt="twitter">
              </div>
              <div class="social-icon" @click="handleSocialLogin('youtube')">
                <img class="icon" src="@/assets/login-options-5.png" alt="youtube">
              </div>
            </div>
          </div>
        </div>
      </transition>
      <transition name="el-fade-in-linear" mode="out-in" leave-active-class="">
        <div class="register-section" v-show="currentStep === 2">
          <div class="select-identity">
            <div class="identity-item s-education-provider" @click="selectIdentity(1)"
              v-if="registerForm.user_type === 1">
              <span class="text">EDUCATION PROVIDER</span>
            </div>
            <div class="identity-item n-education-provider" @click="selectIdentity(1)"
              v-if="[0, 2].includes(registerForm.user_type)">
              <span class="text">EDUCATION PROVIDER</span>
            </div>
            <div class="identity-item s-student" @click="selectIdentity(2)" v-if="registerForm.user_type === 2">
              <span class="text">STUDENT</span>
            </div>
            <div class="identity-item n-student" @click="selectIdentity(2)"
              v-if="[0, 1].includes(registerForm.user_type)">
              <span class="text">STUDENT</span>
            </div>
          </div>
          <el-button type="primary" size="large" class="select-button" @click="handleNext">
            Next
          </el-button>
          <p class="login-link">Already have an account, go to login ></p>
        </div>
      </transition>
      <transition name="el-fade-in-linear" mode="out-in" leave-active-class="">
        <div class="register-section-two" v-show="currentStep === 3">
          <div class="register-header">
            <h2>Education Provider Registration</h2>
          </div>

          <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="register-form">
            <!-- 基本信息 -->
            <div class="form-section">
              <div class="section-title">
                <img class="icon" src="@/assets/basic-information.png" alt="icon">
                <span>Basic information</span>
              </div>

              <div class="form-row">
                <div class="avatar-upload">
                  <FileUpload @uploadData="handleAvatarSuccess">
                    <template #trigger>
                      <div class="avatar-placeholder">
                        <img v-if="avatarPreview" :src="avatarPreview" alt="avatar" class="avatar-preview" />
                        <img v-else class="icon" src="@/assets/avatar-add.png" alt="icon">
                      </div>
                    </template>
                  </FileUpload>
                  <span class="avatar-label">Avatar</span>
                </div>

                <div class="form-inputs">
                  <div class="input-row">
                    <el-form-item prop="username">
                      <el-input v-model="registerForm.username" placeholder="Username" size="large" />
                    </el-form-item>
                    <el-form-item prop="password">
                      <el-input v-model="registerForm.password" type="password" placeholder="Password" size="large"
                        show-password />
                    </el-form-item>
                  </div>
                  <div class="input-row">
                    <el-form-item prop="email">
                      <el-input v-model="registerForm.email" placeholder="Email address" size="large" />
                    </el-form-item>
                    <el-form-item prop="confirm_password">
                      <el-input v-model="registerForm.confirm_password" type="password" placeholder="Confirm password"
                        size="large" show-password />
                    </el-form-item>
                  </div>
                </div>
              </div>
            </div>

            <!-- 教育背景和专业资格 -->
            <div class="form-section">
              <div class="section-title">
                <img class="icon" src="@/assets/educational-icon.png" alt="icon">

                <span>Educational background and professional qualification</span>
              </div>

              <div class="form-row">
                <div class="form-left">
                  <el-form-item prop="education">
                    <el-select v-model="registerForm.education" placeholder="Highest education level" size="large">
                      <el-option v-for="option in educationLevelOptions" :key="option.value" :label="option.label"
                        :value="option.value" />
                    </el-select>
                  </el-form-item>

                  <el-form-item prop="school">
                    <el-input v-model="registerForm.school" placeholder="Name of educational institution"
                      size="large" />
                  </el-form-item>

                  <el-form-item prop="profession">
                    <el-select v-model="registerForm.profession" placeholder="Professional field" size="large">
                      <el-option v-for="option in professionalFieldOptions" :key="option.value" :label="option.label"
                        :value="option.value" />
                    </el-select>
                  </el-form-item>

                  <el-form-item prop="teaching_experience">
                    <el-select v-model="registerForm.teaching_experience" placeholder="Years of teaching experience"
                      size="large">
                      <el-option v-for="option in teachingExperienceOptions" :key="option.value" :label="option.label"
                        :value="option.value" />
                    </el-select>
                  </el-form-item>

                  <el-form-item prop="teaching_method">
                    <el-select v-model="registerForm.teaching_method" placeholder="Preferred teaching method"
                      size="large">
                      <el-option v-for="option in teachingMethodOptions" :key="option.value" :label="option.label"
                        :value="option.value" />
                    </el-select>
                  </el-form-item>
                </div>

                <div class="form-right">
                  <div class="upload-section">
                    <div class="upload-area">
                      <img class="upload-file" src="@/assets/upload-files.png" alt="icon">
                      <span class="text">Upload relevant supporting documents</span>
                    </div>
                    <p class="upload-hint">Here are the names and size specifications of the files available for upload
                    </p>
                    <div class="verification-section">
                      <span>Online database verification: </span>
                      <el-link type="primary">LinkedIn</el-link>
                    </div>
                    <el-button type="primary" class="nft-button">Create NFT</el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 协议和通知 -->
            <div class="form-section">
              <el-form-item prop="agreement">
                <el-checkbox v-model="registerForm.agreement">
                  <span class="agreement">Read agreement</span> <el-link type="primary">《Protocol Link》</el-link>
                </el-checkbox>
              </el-form-item>

              <el-form-item prop="notifications">
                <el-checkbox v-model="registerForm.notifications">
                  <span>Receive course updates and news notifications</span>
                </el-checkbox>
              </el-form-item>
            </div>

            <!-- 提交按钮 -->
            <el-form-item>
              <el-button type="primary" size="large" class="submit-button" @click="handleRegister">
                SUBMIT
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Plus, School, Upload } from '@element-plus/icons-vue'
import FileUpload from '@/components/FileUpload/Index.vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 当前步骤：1-登录，2-选择注册类型，3-教育者注册1，4-教育者注册2，5-学习者注册1，6-学习者注册2
const currentStep = ref(3)

// 表单引用
const loginFormRef = ref()
const registerFormRef = ref()



// 登录表单数据
const loginForm = reactive({
  username: 'Ning Meng',
  password: '123456',
  user_type: 1, // 1-教育者，2-学习者
})

// 头像文件列表
const avatarList = ref([])
const avatarFileList = ref([])
const avatarPreview = ref('')

// 文档文件列表
const documentList = ref([])

// 教育背景选项
const educationLevelOptions = [
  { label: "Bachelor's Degree", value: 'bachelor' },
  { label: "Master's Degree", value: 'master' },
  { label: "Doctoral Degree", value: 'doctoral' },
  { label: 'Other', value: 'other' }
]

// 专业领域选项
const professionalFieldOptions = [
  { label: 'Computer Science', value: 'cs' },
  { label: 'Mathematics', value: 'math' },
  { label: 'Physics', value: 'physics' },
  { label: 'Chemistry', value: 'chemistry' },
  { label: 'Biology', value: 'biology' },
  { label: 'Engineering', value: 'engineering' },
  { label: 'Business', value: 'business' },
  { label: 'Arts', value: 'arts' },
  { label: 'Other', value: 'other' }
]

// 教学经验选项
const teachingExperienceOptions = [
  { label: '0-1 years', value: '0-1' },
  { label: '2-5 years', value: '2-5' },
  { label: '6-10 years', value: '6-10' },
  { label: '10+ years', value: '10+' }
]

// 教学方法选项
const teachingMethodOptions = [
  { label: 'Online', value: 'online' },
  { label: 'Offline', value: 'offline' },
  { label: 'Hybrid', value: 'hybrid' }
]

// 注册表单数据
const registerForm = reactive({
  user_type: 1, // 1-教育者，2-学习者
  avatar: '', // 头像
  username: '', // 账号
  email: '', // 邮箱
  password: '', // 密码
  confirm_password: '', // 确认密码
  education: '', // 教育背景
  school: '', // 毕业院校
  profession: '', // 专业领域
  teaching_experience: '', // 教学经验
  teaching_method: '',
  agreement: false, // 注册协议
  notifications: false // 接收通知
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: 'The account format is incorrect', trigger: 'blur' },
    { min: 3, max: 20, message: 'Username length is between 3 and 20 characters', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'The password is case insensitive and contains letters and numbers', trigger: 'blur' },
    { min: 6, max: 20, message: 'Password length is between 6 and 20 characters', trigger: 'blur' }
  ]
}

// 注册表单验证规则
const registerRules = {
  username: [
    { required: true, message: 'Username is required', trigger: 'blur' },
    { min: 2, max: 50, message: 'Username length should be between 2 and 50 characters', trigger: 'blur' }
  ],
  email: [
    { required: true, message: 'Email is required', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Password is required', trigger: 'blur' },
    { min: 6, max: 20, message: 'Password length should be between 6 and 20 characters', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: 'Please confirm your password', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('Passwords do not match'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  education: [
    { required: true, message: 'Please select your education level', trigger: 'change' }
  ],
  school: [
    { required: true, message: 'School name is required', trigger: 'blur' }
  ],
  profession: [
    { required: true, message: 'Please select your professional field', trigger: 'change' }
  ],
  teaching_experience: [
    { required: true, message: 'Please select your teaching experience', trigger: 'change' }
  ],
  teaching_method: [
    { required: true, message: 'Please select your preferred teaching method', trigger: 'change' }
  ],
  agreement: [
    {
      validator: (_, value, callback) => {
        if (!value) {
          callback(new Error('You must agree to the terms'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 登录身份
const changeUserType = (type) => {
  loginForm.user_type = type
}

// 选择注册身份
const selectIdentity = (identity) => {
  registerForm.user_type = identity
}

// 选择身份下一步
const handleNext = () => {
  if (registerForm.user_type === 0) {
    ElMessage.warning('Please select an identity')
    return
  }
  currentStep.value = 3
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  try {
    // 执行登录
    const result = await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      user_type: loginForm.user_type
    })

    if (result.success) {
      ElMessage.success('Login successful')

      // 获取重定向路径
      const redirectPath = route.query.redirect || '/'

      // 跳转到目标页面
      router.push(redirectPath)
    } else {
      ElMessage.error(result.error || 'Login failed')
    }
  } catch (error) {
    console.error('Login error:', error)
    ElMessage.error('An error occurred during login')
  }
}

// 处理头像上传成功
const handleAvatarSuccess = (response) => {
  console.log('头像上传成功:', response)
  // 更新注册表单中的头像字段
  if (response && response.url) {
    registerForm.avatar = response.url
  }
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    // 验证表单
    const valid = await registerFormRef.value.validate()
    if (!valid) {
      return
    }

    // 准备注册数据
    const registerData = {
      ...registerForm,
      avatar: avatarList.value.length > 0 ? avatarList.value[0].url : ''
    }

    // TODO: 实现实际的注册逻辑
    console.log('注册数据:', registerData)
    ElMessage.success('Registration submitted successfully')

    // 注册成功后可以跳转到登录页面或其他页面
    // currentStep.value = 1 // 跳转到登录页面

  } catch (error) {
    console.error('Registration error:', error)
    ElMessage.error('Registration failed')
  }
}

// 处理社交登录
const handleSocialLogin = (provider) => {
  console.log(`Social login with ${provider}`)
  ElMessage.info(`${provider.charAt(0).toUpperCase() + provider.slice(1)} login is not implemented yet`)

  // TODO: 实现实际的社交登录逻辑
  // 示例实现:
  // switch (provider) {
  //   case 'facebook':
  //     // Facebook 登录逻辑
  //     break
  //   case 'pinterest':
  //     // Pinterest 登录逻辑
  //     break
  //   case 'github':
  //     // GitHub 登录逻辑
  //     break
  //   case 'twitter':
  //     // Twitter 登录逻辑
  //     break
  //   case 'youtube':
  //     // YouTube 登录逻辑
  //     break
  // }
}

// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isAuthenticated) {
    const redirectPath = route.query.redirect || '/'
    router.push(redirectPath)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background-image: url('@/assets/login-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

// 品牌标识
.brand-logo {
  position: absolute;
  top: 58px;
  left: 128px;
  z-index: 10;

  .logo {
    width: 248rpx;
    height: 30rpx;
    user-select: none;
  }
}

// 主要内容区域
.main-content {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-end;

}

.illustration-section {
  width: 800px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .illustration {
    width: 100%;
  }
}

// 右侧登录区域
.login-section {
  width: 540px;
  min-height: 681px;
  display: flex;
  flex-direction: column;
  background: #1C1824;
  border-radius: 20px;
  border: 1px solid #523F3E;
  margin-right: 17.19%;
  padding: 0 45px 53px;
}

.login-header {
  margin: 65px 0 50px;

  .login-welcome {
    width: 138px;
    height: 27px;
  }

  .login-tabs {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 50px;

    .tab-item {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #FEFEFE;
      transition: all 0.3s ease;
      position: relative;
      cursor: pointer;
      user-select: none;

      &.active {
        color: #E34234;
        transition: all 0.3s ease;

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 3px;
          background: #E34234;
        }
      }
    }
  }
}

.login-form {
  width: 100%;

  .el-form-item {
    width: 100%;
    margin-bottom: 20px;
  }

  .input-wrapper {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(153, 125, 122, 0.4);
    margin-bottom: 4px;

    .input-icon {
      color: #e53e3e;
      z-index: 10;
      font-size: 25px;
    }

    :deep(.el-input__wrapper) {
      background: transparent;
      border-radius: 0;
      box-shadow: none;

      &:hover {
        border-color: #e53e3e;
      }
    }

    :deep(.el-input__inner) {
      color: white;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .forgot-password {
    text-align: right;
    margin: 10px 0 40px;

    :deep(.el-link) {
      color: #FEFEFE;
      font-size: 16px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .login-button {
    width: 100%;
    height: 50px;
    font-size: 16px;
    font-weight: 600;
    background: #E34234;
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(229, 62, 62, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.login-footer {
  text-align: center;

  p {
    color: #FEFEFE;
    font-size: 16px;

    .register-link {
      color: #E34234;
      font-size: 16px;

      &:hover {
        color: #c53030;
      }
    }
  }

  .divider {
    width: 270px;
    position: relative;
    margin: 28px auto 15px;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #997D7A;
    }

    span {
      background-color: #1C1824;
      color: #E34234;
      padding: 0 15px;
      font-size: 16px;
      position: relative;
      z-index: 1;
    }
  }

  .social-login {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .social-icon {
      width: 50px;
      height: 50px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .icon {
        width: 50px;
        height: 50px;
      }
    }
  }
}

.register-section {
  width: 900px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #1C1824;
  border-radius: 10px;

  .select-identity {
    width: 446px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .identity-item {
      width: 208px;
      height: 227px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;

      &.s-education-provider {
        background-image: url('@/assets/identity-item-1.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text {
          width: 100%;
          text-align: center;
          margin-top: 20px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #FEFEFE;
          user-select: none;
        }
      }

      &.n-student {
        background-image: url('@/assets/identity-item-2.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text {
          width: 100%;
          text-align: center;
          margin-top: 20px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #C4BDC1;
          user-select: none;
        }
      }

      &.n-education-provider {
        background-image: url('@/assets/identity-item-3.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text {
          width: 100%;
          text-align: center;
          margin-top: 20px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #C4BDC1;
          user-select: none;
        }
      }

      &.s-student {
        background-image: url('@/assets/identity-item-4.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text {
          width: 100%;
          text-align: center;
          margin-top: 20px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #FEFEFE;
          user-select: none;
        }
      }
    }
  }

  .select-button {
    width: 446px;
    height: 50px;
    font-size: 18px;
    font-weight: 600;
    background: #E34234;
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    margin: 80px 0 20px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(229, 62, 62, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .login-link {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #FEFEFE;
    cursor: pointer;
    user-select: none;
  }
}

// 注册表单样式
.register-section-two {
  width: 900px;
  height: 100vh;
  background-color: #1C1824;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  padding: 0 60px;


  .register-header {
    width: 100%;
    margin: 116px 0 102px;

    h2 {
      font-family: CKTKingkong Bold;
      font-weight: bold;
      font-weight: 400;
      font-size: 30px;
      color: #E34234;

    }
  }

  .register-form {
    .form-section {
      margin-bottom: 37px;

      .agreement {
        font-size: 16px;
        color: #BEBDC1;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 4px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
        margin-bottom: 25px;

        .icon {
          width: 24px;
          height: 24px;
        }
      }

      .form-row {
        display: flex;
        gap: 30px;
        align-items: flex-start;

        .avatar-upload {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;

          .avatar-placeholder {
            width: 74px;
            height: 74px;
            background: #25202F;
            border-radius: 50%;
            border: 1px solid #5F4F55;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;

            .icon {
              width: 24px;
              height: 24px;
            }

            &:hover {
              border-color: #c53030;
              background: rgba(227, 66, 52, 0.1);
            }

            .el-icon {
              font-size: 24px;
              color: #E34234;
            }
          }

          .avatar-label {
            color: #FEFEFE;
            font-size: 16px;
          }

          .avatar-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }
        }

        .form-inputs {
          flex: 1;

          .input-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;

            .el-form-item {
              flex: 1;
              margin-bottom: 0;
            }
          }
        }

        .form-left {
          flex: 1;

          .el-form-item {
            margin-bottom: 20px;
          }
        }

        .form-right {
          width: 374px;

          .upload-section {
            .upload-area {
              width: 100%;
              height: 100px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              background-color: #25202F;
              border-radius: 10px;
              border: 1px solid #5F4F55;
              cursor: pointer;
              transition: all 0.3s ease;

              .upload-file {
                width: 42px;
                height: 42px;
                margin-bottom: 10px;
              }

              .text {
                font-weight: 400;
                font-size: 16px;
                color: #FFFFFF;
              }

              &:hover {
                border-color: #c53030;
                background: rgba(227, 66, 52, 0.1);
              }
            }

            .upload-hint {
              font-weight: 400;
              font-size: 16px;
              line-height: 22px;
              color: #5F4F55;
              margin: 8px 0;
            }

            .verification-section {
              width: 100%;
              height: 60px;
              display: flex;
              align-items: center;
              padding-left: 11px;
              background: #25202F;
              border-radius: 10px;
              border: 1px solid #5F4F55;
              margin-bottom: 20px;
              color: #BEBDC1;
              font-size: 16px;

              .el-link {
                color: #D64034;
                margin-left: 5px;
              }
            }

            .nft-button {
              width: 100%;
              background: #E34234;
              border: none;
              border-radius: 8px;
              height: 40px;

              &:hover {
                background: #c53030;
              }
            }
          }
        }
      }
    }

    .submit-button {
      width: 100%;
      height: 50px;
      background: #E34234;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      margin-top: 20px;

      &:hover {
        background: #c53030;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(227, 66, 52, 0.3);
      }
    }

    // 统一输入框样式
    :deep(.el-input__wrapper) {
      background: #25202F;
      border: 1px solid #5F4F55;
      border-radius: 10px;
      box-shadow: none;
      height: 40px;

      &:hover {
        border-color: #E34234;
      }

      &.is-focus {
        border-color: #E34234;
        box-shadow: 0 0 0 2px rgba(227, 66, 52, 0.2);
      }
    }

    :deep(.el-input__inner) {
      color: white;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    :deep(.el-select) {
      width: 100%;

      .el-select__wrapper {
        background: #25202F;
        border: 1px solid #5F4F55;
        border-radius: 10px;
        box-shadow: none;
        height: 40px;

        &:hover {
          border-color: #E34234;
        }

        &.is-focus {
          border-color: #E34234;
          box-shadow: 0 0 0 2px rgba(227, 66, 52, 0.2);
        }
      }
    }

    :deep(.el-checkbox) {
      color: #FEFEFE;

      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #E34234;
        border-color: #E34234;
      }

      .el-checkbox__inner:hover {
        border-color: #E34234;
      }
    }

    :deep(.el-form-item__error) {
      color: #E34234;
    }
  }
}
</style>

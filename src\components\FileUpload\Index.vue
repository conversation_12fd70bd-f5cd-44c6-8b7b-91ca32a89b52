<template>
  <div class="uploading-container">
    <div class="upload-input-div">
      <!-- 自定义上传按钮，替代 el-button -->
      <div class="custom-upload-btn" @click="triggerFileInput">
        <svg class="upload-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor"
            d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
        </svg>
        {{ content }}
        <svg class="upload-arrow" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M7,14L12,9L17,14H7Z" />
        </svg>
      </div>
      <p class="textTips2">{{ tips }}</p>
      <input ref="fileInputRef" accept="image/*" class="file-upload-input" type="file" @change="uploadFile" />
    </div>

    <!-- 图片预览区域 -->
    <div class="upload-img-box" v-if="imgsData.length > 0">
      <div class="upload-img" v-for="(img, index) in imgsData" :key="index">
        <img :src="img" alt="uploaded image" class="img" @click="handlePictureCardPreview(img)" />
        <div class="close" @click="handleRemove(index)">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path fill="currentColor"
              d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
          </svg>
        </div>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="preview-modal" v-if="dialogVisible" @click="closePreview">
      <div class="preview-content" @click.stop>
        <img :src="dialogImageUrl" alt="preview" />
        <button class="close-btn" @click="closePreview">✕</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
// import { postUploadFile } from '@/api/upload'

// Props 定义
const props = defineProps({
  content: {
    type: String,
    default: '上传图片'
  },
  tips: {
    type: String,
    default: '尺寸要求：最小 375*125 最大750*250'
  }
})

// Emits 定义
const emit = defineEmits(['upload-data'])

// 响应式数据
const fileInputRef = ref()
const imgsData = ref([])
const fileUrl = ref('')
const dialogVisible = ref(false)
const dialogImageUrl = ref('')

// 图片上传类型
const accept = 'image/gif, image/jpeg, image/png, image/jpg'

// 触发文件选择
const triggerFileInput = () => {
  fileInputRef.value?.click()
}

// 上传文件 - 图片
const uploadFile = async (event) => {
  const img1 = event.target.files[0]
  if (!img1) return

  const type = img1.type // 文件的类型，判断是否是图片
  const size = img1.size // 文件的大小，判断图片的大小

  // 文件类型验证
  if (accept.indexOf(type) === -1) {
    ElMessage({
      message: '请选择gif,jpeg,png,jpg格式图片上传',
      type: 'warning'
    })
    return false
  }

  // 文件大小验证 (3MB)
  if (size > 3145728) {
    ElMessage({
      message: '请选择3M以内的图片上传',
      type: 'warning'
    })
    return false
  }

  // 浏览器兼容性检查
  if (typeof FileReader === 'undefined') {
    ElMessage({
      message: '抱歉，你的浏览器不支持 FileReader，请使用现代浏览器操作！',
      type: 'error'
    })
    return false
  }

  // 显示加载状态
  const loading = ElLoading.service({ text: '图片上传中...' })

  try {
    // TODO: 替换为实际的上传 API
    // const response = await postUploadFile(img1)

    // 模拟上传成功的响应
    const mockResponse = {
      code: '00000',
      data: {
        ossUrl: URL.createObjectURL(img1) // 临时使用本地预览URL
      }
    }

    if (mockResponse.code === '00000') {
      const uploadedFileUrl = mockResponse.data.ossUrl

      ElMessage({
        message: '上传成功',
        type: 'success'
      })

      // 更新数据
      fileUrl.value = uploadedFileUrl
      imgsData.value.push(uploadedFileUrl)

      // 发送事件
      emit('upload-data', mockResponse.data)
    } else {
      ElMessage({
        message: mockResponse.message || '上传失败',
        type: 'error'
      })
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage({
      message: '上传失败，请重试',
      type: 'error'
    })
  } finally {
    // 关闭加载状态
    setTimeout(() => {
      loading.close()
    }, 500)

    // 清空input值，允许重复选择同一文件
    event.target.value = ''
  }
}

// 删除图片
const handleRemove = (index) => {
  ElMessageBox.confirm('你确定要删除当前项吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    imgsData.value.splice(index, 1)
    ElMessage({
      message: '删除成功',
      type: 'success'
    })
    emit('upload-data', imgsData.value)
  }).catch(() => {
    // 用户取消删除
  })
}

// 预览图片
const handlePictureCardPreview = (imageUrl) => {
  dialogImageUrl.value = imageUrl
  dialogVisible.value = true
}

// 关闭预览
const closePreview = () => {
  dialogVisible.value = false
  dialogImageUrl.value = ''
}
</script>

<style lang="scss" scoped>
.uploading-container {
  width: 100%;
}

.upload-input-div {
  position: relative;
  padding: 0;
}

// 自定义上传按钮样式
.custom-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  user-select: none;

  &:hover {
    background: #66b1ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  .upload-icon,
  .upload-arrow {
    flex-shrink: 0;
  }
}

.file-upload-input {
  display: none;
}

.textTips {
  padding: 0;
  font-size: 12px;
  color: #d40000;
}

.textTips2 {
  padding: 5px 0;
  font-size: 12px;
  line-height: 14px;
  color: #969799;
  margin: 8px 0 0 0;
}

.upload-img-box {
  padding: 0;
  margin: 16px 0 0 0;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.upload-img {
  position: relative;
  box-sizing: border-box;
  width: 148px;
  height: 148px;
  background-color: #fff;
  border: 1px solid #c0ccda;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .close {
      opacity: 1;
      transform: scale(1);
    }
  }

  .img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .close {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 24px;
    height: 24px;
    background: #f56c6c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

    svg {
      color: white;
      width: 16px;
      height: 16px;
    }

    &:hover {
      background: #f78989;
      transform: scale(1.1);
    }
  }
}

// 预览模态框样式
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;

  .preview-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    img {
      display: block;
      max-width: 100%;
      max-height: 80vh;
      object-fit: contain;
    }

    .close-btn {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.5);
      color: white;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.7);
        transform: scale(1.1);
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
</style>

import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'
import { ElMessage, ElMessageBox } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // API基础URL
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求重试配置
const retryConfig = {
  retries: 3, // 重试次数
  retryDelay: 1000, // 重试延迟
  retryCondition: (error) => {
    // 网络错误或5xx错误时重试
    return !error.response || (error.response.status >= 500 && error.response.status <= 599)
  }
}

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    // 添加token到请求头
    if (authStore.getToken) {
      config.headers.Authorization = `Bearer ${authStore.getToken}`
    }
    
    // 添加请求时间戳，防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    // 显示加载状态
    if (config.showLoading !== false) {
      authStore.setLoading(true)
    }
    
    console.log('发送请求:', {
      url: config.url,
      method: config.method,
      params: config.params,
      data: config.data
    })
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const authStore = useAuthStore()
    authStore.setLoading(false)
    
    console.log('收到响应:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    })
    
    // 统一处理响应数据格式
    const { data, code, message } = response.data
    
    // 根据后端约定的状态码处理
    if (code === 200 || code === 0) {
      return data || response.data
    } else if (code === 401) {
      // token过期或无效
      handleAuthError('登录已过期，请重新登录')
      return Promise.reject(new Error(message || '认证失败'))
    } else if (code === 403) {
      // 权限不足
      ElMessage.error(message || '权限不足')
      return Promise.reject(new Error(message || '权限不足'))
    } else {
      // 其他业务错误
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message || '请求失败'))
    }
  },
  async (error) => {
    const authStore = useAuthStore()
    authStore.setLoading(false)
    
    console.error('响应错误:', error)
    
    // 处理网络错误
    if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }
    
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        // 未授权 - token过期或无效
        await handleAuthError('登录已过期，请重新登录')
        break
        
      case 403:
        // 权限不足
        ElMessage.error('权限不足，无法访问该资源')
        break
        
      case 404:
        ElMessage.error('请求的资源不存在')
        break
        
      case 422:
        // 表单验证错误
        const validationErrors = data?.errors || {}
        const errorMessages = Object.values(validationErrors).flat()
        if (errorMessages.length > 0) {
          ElMessage.error(errorMessages[0])
        } else {
          ElMessage.error('请求参数错误')
        }
        break
        
      case 429:
        ElMessage.error('请求过于频繁，请稍后再试')
        break
        
      case 500:
        ElMessage.error('服务器内部错误，请稍后再试')
        break
        
      case 502:
      case 503:
      case 504:
        ElMessage.error('服务暂时不可用，请稍后再试')
        break
        
      default:
        ElMessage.error(data?.message || `请求失败 (${status})`)
    }
    
    // 实现请求重试逻辑
    if (retryConfig.retryCondition(error)) {
      const config = error.config
      config.__retryCount = config.__retryCount || 0
      
      if (config.__retryCount < retryConfig.retries) {
        config.__retryCount++
        console.log(`请求重试第 ${config.__retryCount} 次:`, config.url)
        
        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, retryConfig.retryDelay))
        return request(config)
      }
    }
    
    return Promise.reject(error)
  }
)

// 处理认证错误
const handleAuthError = async (message = '登录已过期，请重新登录') => {
  const authStore = useAuthStore()
  
  try {
    // 尝试刷新token
    const refreshResult = await authStore.refreshToken()
    if (refreshResult.success) {
      console.log('Token刷新成功')
      return
    }
  } catch (error) {
    console.error('Token刷新失败:', error)
  }
  
  // 刷新失败，清除认证信息并跳转到登录页
  authStore.logout()
  
  // 显示确认对话框
  try {
    await ElMessageBox.confirm(
      message,
      '提示',
      {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 跳转到登录页，并保存当前路由用于登录后跳转
    const currentRoute = router.currentRoute.value
    const redirectPath = currentRoute.path !== '/login' ? currentRoute.fullPath : '/'
    
    router.push({
      path: '/login',
      query: { redirect: redirectPath }
    })
  } catch (error) {
    // 用户取消，也跳转到登录页
    router.push('/login')
  }
}

// 导出请求实例
export default request

// 导出常用的请求方法
export const get = (url, params = {}, config = {}) => {
  return request({
    method: 'get',
    url,
    params,
    ...config
  })
}

export const post = (url, data = {}, config = {}) => {
  return request({
    method: 'post',
    url,
    data,
    ...config
  })
}

export const put = (url, data = {}, config = {}) => {
  return request({
    method: 'put',
    url,
    data,
    ...config
  })
}

export const del = (url, config = {}) => {
  return request({
    method: 'delete',
    url,
    ...config
  })
}

export const patch = (url, data = {}, config = {}) => {
  return request({
    method: 'patch',
    url,
    data,
    ...config
  })
}

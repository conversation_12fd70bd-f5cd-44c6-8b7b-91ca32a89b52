// 导入Vue Router的创建路由器函数
import { createRouter, createWebHistory } from "vue-router";
import { ElMessage } from "element-plus";

// 导入页面组件
import WebDesign from "../views/WebDesign/Index.vue";
import Login from "../views/Login/Index.vue";

// 导入认证store
import { useAuthStore } from "@/stores/auth";

// 定义路由配置
const routes = [
  {
    path: "/login",
    name: "Login",
    component: Login,
    meta: {
      title: "WeMaster™ - Login",
      requiresAuth: false,
    },
  },
  {
    path: "/register",
    name: "Register",
    component: () => import("../views/WebDesign/Details.vue"),
    meta: {
      title: "WeMaster™ - Register",
      requiresAuth: false,
    },
  },
  {
    path: "/",
    redirect: "/weDesign",
  },
  {
    path: "/weDesign",
    name: "WebDesign",
    component: WebDesign,
    meta: {
      title: "WeMaster™ - WebDesign",
      requiresAuth: true,
      menu: {
        title: "WeDesign",
        icon: "Opportunity",
        order: 2,
        showInMenu: true,
      },
    },
    children: [
      {
        path: "details",
        name: "Details",
        component: () => import("../views/WebDesign/Details.vue"),
        meta: {
          title: "WeMaster™ - Digital Asset Details",
          requiresAuth: true,
          menu: {
            title: "Digital Asset Details",
            icon: "ChatLineRound",
            showInMenu: false,
          },
        },
      },
    ],
  },
  {
    path: "/weProvide",
    name: "WeProvide",
    component: () => import("../views/WeProvide/Index.vue"),
    meta: {
      title: "WeMaster™ - WeProvide",
      requiresAuth: true,
      menu: {
        title: "WeProvide",
        icon: "DataAnalysis",
        order: 1,
        showInMenu: true,
      },
    },
  },
  {
    path: "/weAnnotate",
    name: "WeAnnotate",
    meta: {
      title: "WeMaster™ - WeAnnotate",
      requiresAuth: true,
      menu: {
        title: "WeAnnotate",
        icon: "EditPen",
        order: 3,
        showInMenu: true,
      },
    },
    children: [
      {
        path: "levelList",
        name: "LevelList",
        component: () => import("../views/WeAnnotate/LevelList.vue"),
        meta: {
          title: "WeMaster™ - Level List",
          requiresAuth: true,
          menu: {
            title: "Level List",
            icon: "List",
            showInMenu: true,
          },
        },
      },
      {
        path: "annotation",
        name: "Annotation",
        component: () => import("../views/WeAnnotate/Annotation.vue"),
        meta: {
          title: "WeMaster™ - Annotation",
          requiresAuth: true,
          menu: {
            title: "Annotation",
            icon: "Edit",
            showInMenu: true,
          },
        },
      },
    ],
  },
  {
    path: "/weCurate",
    name: "WeCurate",
    meta: {
      title: "WeMaster™ - WeCurate",
      requiresAuth: true,
      menu: {
        title: "WeCurate",
        icon: "EditPen",
        order: 4,
        showInMenu: true,
      },
    },
    children: [
      {
        path: "systematicLearning",
        name: "SystematicLearning",
        meta: {
          title: "WeMaster™ - Systematic Learning",
          requiresAuth: true,
          menu: {
            title: "Systematic Learning",
            icon: "List",
            showInMenu: true,
          },
        },
        children: [
          {
            path: "bundle",
            name: "Bundle",
            component: () =>
              import("../views/WeCurate/SystematicLearning/Bundle.vue"),
            meta: {
              title: "WeMaster™ - Bundle",
              requiresAuth: true,
              menu: {
                title: "Bundle",
                icon: "Edit",
                showInMenu: true,
              },
            },
          },
          {
            path: "shortCourse",
            name: "ShortCourse",
            component: () =>
              import("../views/WeCurate/SystematicLearning/ShortCourse.vue"),
            meta: {
              title: "WeMaster™ - ShortCourse",
              requiresAuth: true,
              menu: {
                title: "Short Course",
                icon: "Edit",
                showInMenu: true,
              },
            },
          },
        ],
      },
    ],
  },
  {
    // 404页面重定向到WebDesign
    path: "/:pathMatch(.*)*",
    redirect: "/weDesign",
  },
];

// 创建路由器实例
const router = createRouter({
  // 使用HTML5 History模式
  history: createWebHistory(),
  routes,
});

// 路由守卫 - 在每次路由跳转前执行
router.beforeEach(async (to, _from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }

  // 获取认证store
  const authStore = useAuthStore();

  // 初始化认证状态（仅在首次访问时）
  if (!authStore.token && !authStore.userInfo) {
    authStore.initAuth();
  }

  // 检查路由是否需要认证
  const requiresAuth = to.matched.some((record) => record.meta.requiresAuth);

  if (requiresAuth) {
    // 需要认证的路由
    if (!authStore.isAuthenticated) {
      // 未登录，跳转到登录页
      ElMessage.warning("请先登录");
      next({
        path: "/login",
        query: { redirect: to.fullPath },
      });
      return;
    }

    // 检查token是否即将过期
    if (authStore.checkTokenExpiry()) {
      try {
        const refreshResult = await authStore.refreshToken();
        if (!refreshResult.success) {
          // 刷新失败，跳转到登录页
          ElMessage.error("登录已过期，请重新登录");
          next({
            path: "/login",
            query: { redirect: to.fullPath },
          });
          return;
        }
      } catch (error) {
        console.error("Token刷新失败:", error);
        ElMessage.error("登录已过期，请重新登录");
        next({
          path: "/login",
          query: { redirect: to.fullPath },
        });
        return;
      }
    }
  } else {
    // 不需要认证的路由
    if (to.path === "/login" && authStore.isAuthenticated) {
      // 已登录用户访问登录页，重定向到首页
      next("/");
      return;
    }
  }

  next();
});

// 导出路由器实例
export default router;

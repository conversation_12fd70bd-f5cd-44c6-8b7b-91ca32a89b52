<template>
  <div class="weprovide-container">
    <div class="cerate-course">
      <div class="left-item">
        <img class="icon" src="@/assets/header-logo.png" alt="logo">
        <p class="introduce">All-In-One Intelligent Instructor Support System</p>
        <div class="button">
          <el-icon color="#E34234" :size="30" class="no-inherit">
            <Plus />
          </el-icon>
          <span class="text">Create</span>
        </div>
      </div>
      <img class="right-item" src="@/assets/workbench.png" />
    </div>
  </div>
</template>

<script setup>
// 导入Vue组合式API
import { onMounted } from 'vue'

onMounted(() => {
  console.log('WeProvide页面已加载')
})
</script>

<style lang="scss" scoped>
.weprovide-container {
  width: 100%;

  .cerate-course {
    width: 100%;
    margin-top: 10%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 100px 0 50px;
    box-sizing: border-box;

    .left-item {
      display: flex;
      flex-direction: column;

      .icon {
        width: 185px;
        height: 22px;
      }

      .introduce {
        font-weight: 500;
        font-size: 30px;
        color: #FFFFFF;
        margin: 15px 0 30px;
      }

      .button {
        width: 160px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(227, 66, 52, 0.2);
        border-radius: 10px;
        display: flex;
        cursor: pointer;
        transition: all 0.3s ease;

        .no-inherit {
          font-weight: bold;
        }

        .text {
          font-weight: 400;
          font-size: 25px;
          color: #E34234;
          margin-left: 10px;
        }

        &:hover {
          background: rgba(227, 66, 52, 0.3);
        }
      }
    }

    .right-item {
      width: 600px;
      height: 600px;
    }
  }
}
</style>

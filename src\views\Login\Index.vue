<template>
  <div class="login-container">
    <div class="brand-logo">
      <img class="logo" src="@/assets/login-logo.png">
    </div>
    <div class="main-content">
      <div class="login-section">
        <div class="login-header">
          <img class="login-welcome" src="@/assets/login-welcome.png" />
          <div class="login-tabs">
            <div class="tab-item" :class="{ active: loginType === 'teacher' }" @click="loginType = 'teacher'">
              TEACHER SIDE LOGIN
            </div>
            <div class="tab-item" :class="{ active: loginType === 'student' }" @click="loginType = 'student'">
              STUDENT LOGIN
            </div>
          </div>
        </div>

        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form"
          @submit.prevent="handleLogin">
          <el-form-item prop="username">
            <div class="input-wrapper">
              <el-icon class="input-icon">
                <User />
              </el-icon>
              <el-input v-model="loginForm.username" placeholder="Username" size="large" clearable
                @keyup.enter="handleLogin" />
            </div>
            <div v-if="usernameError" class="error-message">The account format is incorrect</div>
          </el-form-item>

          <el-form-item prop="password">
            <div class="input-wrapper">
              <el-icon class="input-icon">
                <Lock />
              </el-icon>
              <el-input v-model="loginForm.password" type="password" placeholder="Password" size="large" show-password
                clearable @keyup.enter="handleLogin" />
            </div>
            <div v-if="passwordError" class="error-message">The password is case sensitive and contains letters and
              numbers</div>
          </el-form-item>

          <div class="forgot-password">
            <el-link type="primary" underline="never">Forgot Password</el-link>
          </div>

          <el-form-item>
            <el-button type="primary" size="large" class="login-button" :loading="authStore.isLoading"
              @click="handleLogin">
              Login
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <p>No account? <el-link type="primary" underline="never" class="register-link">Register now</el-link></p>

          <div class="divider">
            <span>OR</span>
          </div>

          <!-- 社交登录 -->
          <div class="social-login">
            <div class="social-icon facebook">
              <i class="fab fa-facebook-f"></i>
            </div>
            <div class="social-icon pinterest">
              <i class="fab fa-pinterest-p"></i>
            </div>
            <div class="social-icon github">
              <i class="fab fa-github"></i>
            </div>
            <div class="social-icon twitter">
              <i class="fab fa-twitter"></i>
            </div>
            <div class="social-icon youtube">
              <i class="fab fa-youtube"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

// 登录类型
const loginType = ref('teacher')

// 错误状态
const usernameError = ref(false)
const passwordError = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: 'Ning Meng',
  password: '123456',
  remember: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  // 重置错误状态
  usernameError.value = false
  passwordError.value = false

  try {
    // 验证表单
    const valid = await loginFormRef.value.validate()
    if (!valid) {
      // 显示错误提示
      if (!loginForm.username) {
        usernameError.value = true
      }
      if (!loginForm.password) {
        passwordError.value = true
      }
      return
    }

    // 执行登录
    const result = await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      remember: loginForm.remember,
      type: loginType.value
    })

    if (result.success) {
      ElMessage.success('登录成功')

      // 获取重定向路径
      const redirectPath = route.query.redirect || '/'

      // 跳转到目标页面
      router.push(redirectPath)
    } else {
      ElMessage.error(result.error || '登录失败')
      // 根据错误类型显示相应的错误提示
      if (result.error && result.error.includes('用户名')) {
        usernameError.value = true
      }
      if (result.error && result.error.includes('密码')) {
        passwordError.value = true
      }
    }
  } catch (error) {
    console.error('登录错误:', error)
    ElMessage.error('登录过程中发生错误')
  }
}

// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isAuthenticated) {
    const redirectPath = route.query.redirect || '/'
    router.push(redirectPath)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background-image: url('@/assets/login-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

// 品牌标识
.brand-logo {
  position: absolute;
  top: 58px;
  left: 128px;
  z-index: 10;

  .logo {
    width: 248rpx;
    height: 30rpx;
  }
}

// 主要内容区域
.main-content {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-end;

}

.illustration-section {
  width: 800px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .illustration {
    width: 100%;
  }
}

// 右侧登录区域
.login-section {
  width: 540px;
  height: 681px;
  display: flex;
  flex-direction: column;
  background: #1C1824;
  border-radius: 20px;
  border: 1px solid #523F3E;
  margin-right: 17.19%;
  padding: 0 45px;
}

.login-header {
  margin: 65px 0 50px;

  .login-welcome {
    width: 138px;
    height: 27px;
  }

  .login-tabs {
    display: flex;
    align-items: center;

    .tab-item {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #E34234;
      transition: all 0.3s ease;
      position: relative;

      &.active {
        color: #e53e3e;
        transition: all 0.3s ease;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 2px;
          background: #e53e3e;
          border-radius: 1px;
        }
      }
    }
  }
}

.login-form {
  width: 100%;

  .el-form-item {
    width: 100%;
    margin-bottom: 20px;
  }

  .input-wrapper {
    width: 100%;
    position: relative;

    .input-icon {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      color: rgba(255, 255, 255, 0.6);
      z-index: 10;
      font-size: 18px;
    }

    :deep(.el-input__wrapper) {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      padding-left: 45px;
      box-shadow: none;

      &:hover {
        border-color: rgba(255, 255, 255, 0.3);
      }

      &.is-focus {
        border-color: #e53e3e;
        box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.2);
      }
    }

    :deep(.el-input__inner) {
      color: white;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .error-message {
    color: #e53e3e;
    font-size: 12px;
    margin-top: 5px;
    margin-left: 5px;
  }

  .forgot-password {
    text-align: right;
    margin-bottom: 20px;

    :deep(.el-link) {
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;

      &:hover {
        color: white;
      }
    }
  }

  .login-button {
    width: 100%;
    height: 50px;
    font-size: 16px;
    font-weight: 600;
    background: #e53e3e;
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover {
      background: #c53030;
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(229, 62, 62, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 30px;

  p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin: 0 0 20px 0;

    .register-link {
      color: #e53e3e;
      font-weight: 600;

      &:hover {
        color: #c53030;
      }
    }
  }

  .divider {
    position: relative;
    margin: 20px 0;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: rgba(255, 255, 255, 0.2);
    }

    span {
      background: rgba(45, 55, 72, 0.95);
      color: rgba(255, 255, 255, 0.5);
      padding: 0 15px;
      font-size: 12px;
      position: relative;
      z-index: 1;
    }
  }

  .social-login {
    display: flex;
    justify-content: center;
    gap: 15px;

    .social-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      i {
        font-size: 16px;
        color: white;
      }

      &.facebook {
        background: #3b5998;

        &:hover {
          background: #2d4373;
          transform: translateY(-2px);
        }
      }

      &.pinterest {
        background: #bd081c;

        &:hover {
          background: #8c0615;
          transform: translateY(-2px);
        }
      }

      &.github {
        background: #333;

        &:hover {
          background: #24292e;
          transform: translateY(-2px);
        }
      }

      &.twitter {
        background: #1da1f2;

        &:hover {
          background: #0d8bd9;
          transform: translateY(-2px);
        }
      }

      &.youtube {
        background: #ff0000;

        &:hover {
          background: #cc0000;
          transform: translateY(-2px);
        }
      }
    }
  }
}
</style>

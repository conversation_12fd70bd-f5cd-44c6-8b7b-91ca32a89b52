<template>
  <div class="login-container">
    <div class="brand-logo">
      <img class="logo" src="@/assets/login-logo.png">
    </div>
    <div class="main-content">
      <div class="login-section">
        <div class="login-header">
          <img class="login-welcome" src="@/assets/login-welcome.png" />
          <div class="login-tabs">
            <div class="tab-item" :class="{ active: loginType === 'teacher' }" @click="loginType = 'teacher'">
              TEACHER SIDE LOGIN
            </div>
            <div class="tab-item" :class="{ active: loginType === 'student' }" @click="loginType = 'student'">
              STUDENT LOGIN
            </div>
          </div>
        </div>

        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form"
          @submit.prevent="handleLogin">
          <el-form-item prop="username">
            <div class="input-wrapper">
              <el-icon class="input-icon">
                <User />
              </el-icon>
              <el-input v-model="loginForm.username" placeholder="Username" size="large" clearable
                @keyup.enter="handleLogin" />
            </div>
          </el-form-item>

          <el-form-item prop="password">
            <div class="input-wrapper">
              <el-icon class="input-icon">
                <Lock />
              </el-icon>
              <el-input v-model="loginForm.password" type="password" placeholder="Password" size="large" show-password
                clearable @keyup.enter="handleLogin" />
            </div>
          </el-form-item>

          <div class="forgot-password">
            <el-link type="primary" underline="never">Forgot Password</el-link>
          </div>

          <el-form-item>
            <el-button type="primary" size="large" class="login-button" :loading="authStore.isLoading"
              @click="handleLogin">
              Login
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <p>No account? <el-link type="primary" underline="never" class="register-link">Register now</el-link></p>

          <div class="divider">
            <span>OR</span>
          </div>
          <div class="social-login">
            <div class="social-icon" @click="handleSocialLogin('facebook')">
              <img class="icon" src="@/assets/login-options-1.png" alt="facebook">
            </div>
            <div class="social-icon" @click="handleSocialLogin('pinterest')">
              <img class="icon" src="@/assets/login-options-2.png" alt="pinterest">
            </div>
            <div class="social-icon" @click="handleSocialLogin('github')">
              <img class="icon" src="@/assets/login-options-3.png" alt="github">
            </div>
            <div class="social-icon" @click="handleSocialLogin('twitter')">
              <img class="icon" src="@/assets/login-options-4.png" alt="twitter">
            </div>
            <div class="social-icon" @click="handleSocialLogin('youtube')">
              <img class="icon" src="@/assets/login-options-5.png" alt="youtube">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

const loginType = ref('teacher')

const loginForm = reactive({
  username: 'Ning Meng',
  password: '123456',
  remember: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: 'The account format is incorrect', trigger: 'blur' },
    { min: 3, max: 20, message: 'Username length is between 3 and 20 characters', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'The password is case insensitive and contains letters and numbers', trigger: 'blur' },
    { min: 6, max: 20, message: 'Password length is between 6 and 20 characters', trigger: 'blur' }
  ]
}

// Handle login
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // Validate form
    const valid = await loginFormRef.value.validate()
    if (!valid) {
      return
    }

    // Execute login
    const result = await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      remember: loginForm.remember,
      type: loginType.value
    })

    if (result.success) {
      ElMessage.success('Login successful')

      // Get redirect path
      const redirectPath = route.query.redirect || '/'

      // Navigate to target page
      router.push(redirectPath)
    } else {
      ElMessage.error(result.error || 'Login failed')
    }
  } catch (error) {
    console.error('Login error:', error)
    ElMessage.error('An error occurred during login')
  }
}

// Handle social login
const handleSocialLogin = (provider) => {
  console.log(`Social login with ${provider}`)
  ElMessage.info(`${provider.charAt(0).toUpperCase() + provider.slice(1)} login is not implemented yet`)

  // TODO: Implement actual social login logic
  // Example implementation:
  // switch (provider) {
  //   case 'facebook':
  //     // Facebook login logic
  //     break
  //   case 'pinterest':
  //     // Pinterest login logic
  //     break
  //   case 'github':
  //     // GitHub login logic
  //     break
  //   case 'twitter':
  //     // Twitter login logic
  //     break
  //   case 'youtube':
  //     // YouTube login logic
  //     break
  // }
}

// Check if already logged in when component is mounted
onMounted(() => {
  if (authStore.isAuthenticated) {
    const redirectPath = route.query.redirect || '/'
    router.push(redirectPath)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background-image: url('@/assets/login-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

// 品牌标识
.brand-logo {
  position: absolute;
  top: 58px;
  left: 128px;
  z-index: 10;

  .logo {
    width: 248rpx;
    height: 30rpx;
  }
}

// 主要内容区域
.main-content {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-end;

}

.illustration-section {
  width: 800px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .illustration {
    width: 100%;
  }
}

// Right side login area
.login-section {
  width: 540px;
  min-height: 681px;
  display: flex;
  flex-direction: column;
  background: #1C1824;
  border-radius: 20px;
  border: 1px solid #523F3E;
  margin-right: 17.19%;
  padding: 0 45px 53px;
}

.login-header {
  margin: 65px 0 50px;

  .login-welcome {
    width: 138px;
    height: 27px;
  }

  .login-tabs {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 50px;

    .tab-item {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #FEFEFE;
      transition: all 0.3s ease;
      position: relative;
      cursor: pointer;
      user-select: none;

      &.active {
        color: #E34234;
        transition: all 0.3s ease;

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 3px;
          background: #E34234;
        }
      }
    }
  }
}

.login-form {
  width: 100%;

  .el-form-item {
    width: 100%;
    margin-bottom: 20px;
  }

  .input-wrapper {
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(153, 125, 122, 0.4);
    margin-bottom: 4px;

    .input-icon {
      color: #e53e3e;
      z-index: 10;
      font-size: 25px;
    }

    :deep(.el-input__wrapper) {
      background: transparent;
      border-radius: 0;
      box-shadow: none;

      &:hover {
        border-color: #e53e3e;
      }
    }

    :deep(.el-input__inner) {
      color: white;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .forgot-password {
    text-align: right;
    margin: 10px 0 40px;

    :deep(.el-link) {
      color: #FEFEFE;
      font-size: 16px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .login-button {
    width: 100%;
    height: 50px;
    font-size: 16px;
    font-weight: 600;
    background: #E34234;
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(229, 62, 62, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.login-footer {
  text-align: center;

  p {
    color: #FEFEFE;
    font-size: 16px;

    .register-link {
      color: #E34234;
      font-size: 16px;

      &:hover {
        color: #c53030;
      }
    }
  }

  .divider {
    width: 270px;
    position: relative;
    margin: 28px auto 15px;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #997D7A;
    }

    span {
      background-color: #1C1824;
      color: #E34234;
      padding: 0 15px;
      font-size: 16px;
      position: relative;
      z-index: 1;
    }
  }

  .social-login {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .social-icon {
      width: 50px;
      height: 50px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .icon {
        width: 50px;
        height: 50px;
      }
    }
  }
}
</style>
